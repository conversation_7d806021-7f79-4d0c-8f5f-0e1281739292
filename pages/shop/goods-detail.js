// pages/shop/goods-detail.js
Page({
  data: {
    goods: null,
    quantity: 1,
    imageIndex: 0,
    selectedSku: {},
    currentPrice: 0
  },

  onLoad: function (options) {
    const id = options.id;
    // 模拟获取商品详情数据
    const goodsData = [
      {
        id: 1,
        name: '优质鹅饲料',
        price: 99.99,
        originalPrice: 129.99,
        image: '/images/icons/goods1.png',
        images: ['/images/icons/goods1.png', '/images/icons/goods2.png', '/images/icons/goods3.png'],
        skuList: [
          {
            name: '规格',
            values: [{value: '1公斤'}, {value: '5公斤'}, {value: '10公斤'}]
          },
          {
            name: '类型',
            values: [{value: '普通型'}, {value: '增强型'}]
          }
        ],
        detailImages: ['/images/icons/goods1.png', '/images/icons/goods2.png'],
        detailContent: '专为鹅类设计的高营养饵料，含有丰富的蛋白质、维生素和矿物质，能够有效促进鹅类的健康成长。',
        params: [
          {name: '品牌', value: '智慧养鹅'},
          {name: '产地', value: '中国'},
          {name: '保质期', value: '12个月'},
          {name: '存储方式', value: '阴凉干燥处'}
        ],
        rating: 4.8,
        reviewCount: 156,
        reviews: [
          {
            id: 1,
            username: '小王',
            avatar: '/images/default_avatar.png',
            rating: 5,
            date: '2024-01-15',
            content: '非常好的饵料，我家的鹅吃了长得很快，营养丰富，价格也实惠。',
            images: ['/images/icons/goods1.png']
          },
          {
            id: 2,
            username: '养鹅专家',
            avatar: '/images/default_avatar.png',
            rating: 5,
            date: '2024-01-10',
            content: '使用了一个月，效果明显，鹅类的食欲和活力都很好。',
            images: []
          }
        ],
        category: 1,
        description: '专为鹅类设计的高营养饲料，促进健康成长，富含蛋白质和维生素，适合各年龄段的鹅类食用。',
        stock: 100,
        sales: 1200
      },
      {
        id: 2,
        name: '疫苗套装',
        price: 199.99,
        originalPrice: 249.99,
        image: '/images/icons/goods2.png',
        images: ['/images/icons/goods2.png', '/images/icons/goods3.png'],
        category: 2,
        description: '预防常见鹅类疾病的疫苗组合套装，包含最常见的病毒性疾病预防疫苗，使用方便，效果显著。',
        stock: 50,
        sales: 350
      },
      {
        id: 3,
        name: '养殖设备',
        price: 299.99,
        originalPrice: 399.99,
        image: '/images/icons/goods3.png',
        images: ['/images/icons/goods3.png', '/images/icons/goods1.png'],
        category: 3,
        description: '现代化鹅类养殖设备，提高养殖效率，自动喂食和清洁系统，节省大量人工成本。',
        stock: 20,
        sales: 89
      },
      {
        id: 4,
        name: '营养补充剂',
        price: 159.99,
        originalPrice: 199.99,
        image: '/images/icons/goods4.png',
        images: ['/images/icons/goods4.png'],
        category: 1,
        description: '维生素和矿物质补充剂，增强鹅的免疫力，特别适合在换季或疾病高发期使用。',
        stock: 200,
        sales: 780
      },
      {
        id: 5,
        name: '清洁消毒剂',
        price: 79.99,
        originalPrice: 99.99,
        image: '/images/icons/goods5.png',
        images: ['/images/icons/goods5.png', '/images/icons/goods6.png'],
        category: 2,
        description: '高效消毒剂，保持鹅舍环境清洁卫生，对多种细菌和病毒有强力杀灭作用，安全无害。',
        stock: 150,
        sales: 512
      },
      {
        id: 6,
        name: '智能喂食器',
        price: 499.99,
        originalPrice: 599.99,
        image: '/images/icons/goods6.png',
        images: ['/images/icons/goods6.png', '/images/icons/goods1.png', '/images/icons/goods2.png'],
        category: 3,
        description: '自动化定时喂食设备，节省人工成本，可根据鹅的成长阶段调整喂食量和时间，智能可靠。',
        stock: 15,
        sales: 65
      }
    ];
    
    const goods = goodsData.find(item => item.id == id);
    if (goods) {
      this.setData({
        goods: goods,
        currentPrice: goods.price
      });
    } else {
      wx.showToast({
        title: '商品不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onQuantityChange: function(e) {
    const value = e.detail.value;
    if (value > 0) {
      this.setData({
        quantity: parseInt(value)
      });
    }
  },
  
  onQuantityMinus: function() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },
  
  onQuantityPlus: function() {
    if (this.data.quantity < this.data.goods.stock) {
      this.setData({
        quantity: this.data.quantity + 1
      });
    }
  },

  onAddToCart: function() {
    const goods = this.data.goods;
    const quantity = this.data.quantity;
    
    // 从本地存储获取购物车数据
    let cart = wx.getStorageSync('cart') || [];
    
    // 查找是否已存在该商品
    const existingItemIndex = cart.findIndex(item => item.id === goods.id);
    
    if (existingItemIndex > -1) {
      // 如果已存在，增加数量
      cart[existingItemIndex].quantity += quantity;
    } else {
      // 如果不存在，添加新商品
      cart.push({
        id: goods.id,
        name: goods.name,
        price: goods.price,
        quantity: quantity
      });
    }
    
    // 保存到本地存储
    wx.setStorageSync('cart', cart);
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1000
    });
    
    // 更新购物车数量（通过事件传递）
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 上一个页面
    if (prevPage) {
      prevPage.onShow(); // 触发上一个页面的onShow方法更新购物车数量
    }
  },
  
  onBuyNow: function() {
    const goods = this.data.goods;
    const quantity = this.data.quantity;
    const selectedSku = this.data.selectedSku;

    // 参数验证
    if (!goods) {
      wx.showToast({
        title: '商品信息加载中',
        icon: 'none'
      });
      return;
    }

    if (!goods.id) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      return;
    }

    // 构建跳转参数
    const params = new URLSearchParams({
      goodsId: goods.id,
      quantity: quantity,
      type: 'buynow'
    });

    if (selectedSku && Object.keys(selectedSku).length > 0) {
      params.append('sku', JSON.stringify(selectedSku));
    }

    // 跳转到确认订单页面
    const checkoutUrl = `/pages/shop/checkout?${params.toString()}`;

    wx.navigateTo({
      url: checkoutUrl,
      fail: (error) => {
        console.error('立即购买跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // SKU选择功能
  onSkuSelect: function(e) {
    const { group, value } = e.currentTarget.dataset;
    const selectedSku = { ...this.data.selectedSku };
    selectedSku[group] = value;
    
    // 模拟价格计算（实际中应该根据SKU组合查询价格）
    let newPrice = this.data.goods.price;
    if (selectedSku['规格'] === '5公斤') {
      newPrice = this.data.goods.price * 4.5;
    } else if (selectedSku['规格'] === '10公斤') {
      newPrice = this.data.goods.price * 8.5;
    }
    if (selectedSku['类型'] === '增强型') {
      newPrice = newPrice * 1.2;
    }
    
    this.setData({
      selectedSku: selectedSku,
      currentPrice: newPrice.toFixed(2)
    });
  },

  // 查看所有评价
  onViewAllReviews: function() {
    wx.navigateTo({
      url: `/pages/shop/reviews?goodsId=${this.data.goods.id}`
    });
  }
});